@echo off
REM Script para rodar SatMobWeb com Java local sem conflitar com instalacao do sistema
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

setlocal

REM =============================================================================
REM CONFIGURACOES - EDITE ESTAS VARIAVEIS CONFORME SUA INSTALACAO
REM =============================================================================

REM Caminho para a instalacao local do Java (JDK ou JRE)
REM Exemplo: C:\java\jdk-11.0.20 ou C:\java\jdk-8u391
set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20

REM Caminho para o GlassFish Server (se necessario)
REM Exemplo: C:\glassfish\glassfish5
set GLASSFISH_HOME=C:\glassfish\glassfish5

REM Porta do servidor (padrao GlassFish: 8080)
set SERVER_PORT=8080

REM =============================================================================
REM VALIDACOES
REM =============================================================================

echo [INFO] Verificando instalacao local do Java...

if not exist "%LOCAL_JAVA_HOME%" (
    echo [ERRO] Diretorio do Java nao encontrado: %LOCAL_JAVA_HOME%
    echo [INFO] Por favor, edite a variavel LOCAL_JAVA_HOME no script
    echo [INFO] Exemplo: set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20
    pause
    exit /b 1
)

if not exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
    echo [ERRO] Executavel java.exe nao encontrado em: %LOCAL_JAVA_HOME%\bin\
    echo [INFO] Verifique se o caminho esta correto
    pause
    exit /b 1
)

REM =============================================================================
REM CONFIGURACAO DO AMBIENTE
REM =============================================================================

echo [INFO] Configurando ambiente com Java local...

REM Salva as variaveis originais para restaurar depois
set ORIGINAL_JAVA_HOME=%JAVA_HOME%
set ORIGINAL_PATH=%PATH%

REM Define o Java local como prioritario
set JAVA_HOME=%LOCAL_JAVA_HOME%
set PATH=%LOCAL_JAVA_HOME%\bin;%PATH%

REM Configuracoes do GlassFish (se especificado)
if exist "%GLASSFISH_HOME%" (
    echo [INFO] Configurando GlassFish: %GLASSFISH_HOME%
    set AS_JAVA=%LOCAL_JAVA_HOME%
    set PATH=%GLASSFISH_HOME%\bin;%PATH%
)

REM =============================================================================
REM INFORMACOES DO AMBIENTE
REM =============================================================================

echo.
echo [INFO] ========================================
echo [INFO] CONFIGURACAO DO AMBIENTE
echo [INFO] ========================================
echo [INFO] Java Home: %JAVA_HOME%
echo [INFO] Java Version:
"%JAVA_HOME%\bin\java.exe" -version
echo [INFO] GlassFish: %GLASSFISH_HOME%
echo [INFO] Porta: %SERVER_PORT%
echo [INFO] ========================================
echo.

REM =============================================================================
REM OPCOES DE EXECUCAO
REM =============================================================================

echo [INFO] Escolha uma opcao:
echo [1] Compilar e executar (ant run)
echo [2] Apenas compilar (ant compile)
echo [3] Limpar e compilar (ant clean compile)
echo [4] Gerar WAR (ant dist)
echo [5] Executar testes (ant test)
echo [6] Iniciar GlassFish manualmente
echo [7] Parar GlassFish
echo [8] Status do GlassFish
echo [0] Sair
echo.

set /p opcao="Digite sua opcao (0-8): "

if "%opcao%"=="1" goto :run_app
if "%opcao%"=="2" goto :compile_only
if "%opcao%"=="3" goto :clean_compile
if "%opcao%"=="4" goto :build_war
if "%opcao%"=="5" goto :run_tests
if "%opcao%"=="6" goto :start_glassfish
if "%opcao%"=="7" goto :stop_glassfish
if "%opcao%"=="8" goto :status_glassfish
if "%opcao%"=="0" goto :end
goto :invalid_option

REM =============================================================================
REM FUNCOES
REM =============================================================================

:run_app
echo [INFO] Compilando e executando aplicacao...
ant run
goto :end

:compile_only
echo [INFO] Compilando aplicacao...
ant compile
goto :end

:clean_compile
echo [INFO] Limpando e compilando aplicacao...
ant clean compile
goto :end

:build_war
echo [INFO] Gerando arquivo WAR...
ant dist
echo [INFO] WAR gerado em: dist\SatMobWeb.war
goto :end

:run_tests
echo [INFO] Executando testes...
ant test
goto :end

:start_glassfish
if not exist "%GLASSFISH_HOME%" (
    echo [ERRO] GlassFish nao configurado ou nao encontrado
    goto :end
)
echo [INFO] Iniciando GlassFish Server...
"%GLASSFISH_HOME%\bin\asadmin.bat" start-domain
echo [INFO] GlassFish iniciado. Acesse: http://localhost:%SERVER_PORT%
goto :end

:stop_glassfish
if not exist "%GLASSFISH_HOME%" (
    echo [ERRO] GlassFish nao configurado ou nao encontrado
    goto :end
)
echo [INFO] Parando GlassFish Server...
"%GLASSFISH_HOME%\bin\asadmin.bat" stop-domain
goto :end

:status_glassfish
if not exist "%GLASSFISH_HOME%" (
    echo [ERRO] GlassFish nao configurado ou nao encontrado
    goto :end
)
echo [INFO] Status do GlassFish Server...
"%GLASSFISH_HOME%\bin\asadmin.bat" list-domains
goto :end

:invalid_option
echo [ERRO] Opcao invalida!
goto :end

:end
echo.
echo [INFO] Operacao concluida.
echo [INFO] Pressione qualquer tecla para sair...
pause >nul

REM Restaura as variaveis originais (opcional, pois o script termina)
REM set JAVA_HOME=%ORIGINAL_JAVA_HOME%
REM set PATH=%ORIGINAL_PATH%

endlocal
