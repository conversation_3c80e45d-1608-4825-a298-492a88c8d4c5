@echo off
REM Script para executar aplicacoes Java com instalacao local sem conflitar com Java do sistema
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

setlocal

REM =============================================================================
REM CONFIGURACOES - EDITE ESTAS VARIAVEIS CONFORME SUA INSTALACAO
REM =============================================================================

REM Caminho para a instalacao local do Java (JDK ou JRE)
REM Exemplo: C:\java\jdk-11.0.20 ou C:\java\jdk-8u391
set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20

REM Configuracoes da aplicacao Java a ser executada
REM Caminho para o arquivo JAR principal
set JAR_FILE=app.jar

REM Classe principal (se nao for JAR executavel)
set MAIN_CLASS=

REM Classpath adicional (separado por ;)
set ADDITIONAL_CLASSPATH=

REM Argumentos da JVM (memoria, etc)
set JVM_ARGS=-Xmx512m -Xms256m

REM Argumentos da aplicacao
set APP_ARGS=

REM Diretorio de trabalho da aplicacao
set WORK_DIR=%CD%

REM =============================================================================
REM VALIDACOES
REM =============================================================================

echo [INFO] Verificando instalacao local do Java...

if not exist "%LOCAL_JAVA_HOME%" (
    echo [ERRO] Diretorio do Java nao encontrado: %LOCAL_JAVA_HOME%
    echo [INFO] Por favor, edite a variavel LOCAL_JAVA_HOME no script
    echo [INFO] Exemplo: set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20
    pause
    exit /b 1
)

if not exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
    echo [ERRO] Executavel java.exe nao encontrado em: %LOCAL_JAVA_HOME%\bin\
    echo [INFO] Verifique se o caminho esta correto
    pause
    exit /b 1
)

REM =============================================================================
REM CONFIGURACAO DO AMBIENTE
REM =============================================================================

echo [INFO] Configurando ambiente com Java local...

REM Salva as variaveis originais para restaurar depois
set ORIGINAL_JAVA_HOME=%JAVA_HOME%
set ORIGINAL_PATH=%PATH%

REM Define o Java local como prioritario
set JAVA_HOME=%LOCAL_JAVA_HOME%
set PATH=%LOCAL_JAVA_HOME%\bin;%PATH%

REM Monta o classpath completo
set FULL_CLASSPATH=%JAR_FILE%
if not "%ADDITIONAL_CLASSPATH%"=="" (
    set FULL_CLASSPATH=%FULL_CLASSPATH%;%ADDITIONAL_CLASSPATH%
)

REM =============================================================================
REM INFORMACOES DO AMBIENTE
REM =============================================================================

echo.
echo [INFO] ========================================
echo [INFO] CONFIGURACAO DO AMBIENTE
echo [INFO] ========================================
echo [INFO] Java Home: %JAVA_HOME%
echo [INFO] Java Version:
"%JAVA_HOME%\bin\java.exe" -version
echo [INFO] JAR File: %JAR_FILE%
echo [INFO] Main Class: %MAIN_CLASS%
echo [INFO] Work Dir: %WORK_DIR%
echo [INFO] JVM Args: %JVM_ARGS%
echo [INFO] App Args: %APP_ARGS%
echo [INFO] ========================================
echo.

REM =============================================================================
REM OPCOES DE EXECUCAO
REM =============================================================================

echo [INFO] Escolha uma opcao:
echo [1] Executar JAR (-jar %JAR_FILE%)
echo [2] Executar classe principal (%MAIN_CLASS%)
echo [3] Executar JAR com argumentos personalizados
echo [4] Executar classe com argumentos personalizados
echo [5] Mostrar informacoes do JAR
echo [6] Listar classes no JAR
echo [7] Verificar dependencias
echo [8] Modo interativo (prompt Java)
echo [9] Configurar aplicacao
echo [0] Sair
echo.

set /p opcao="Digite sua opcao (0-9): "

if "%opcao%"=="1" goto :run_jar
if "%opcao%"=="2" goto :run_class
if "%opcao%"=="3" goto :run_jar_custom
if "%opcao%"=="4" goto :run_class_custom
if "%opcao%"=="5" goto :jar_info
if "%opcao%"=="6" goto :list_classes
if "%opcao%"=="7" goto :check_dependencies
if "%opcao%"=="8" goto :interactive_mode
if "%opcao%"=="9" goto :configure_app
if "%opcao%"=="0" goto :end
goto :invalid_option

REM =============================================================================
REM FUNCOES
REM =============================================================================

:run_jar
echo [INFO] Executando JAR: %JAR_FILE%
if not exist "%JAR_FILE%" (
    echo [ERRO] Arquivo JAR nao encontrado: %JAR_FILE%
    goto :end
)
cd /d "%WORK_DIR%"
"%JAVA_HOME%\bin\java.exe" %JVM_ARGS% -jar "%JAR_FILE%" %APP_ARGS%
goto :end

:run_class
if "%MAIN_CLASS%"=="" (
    echo [ERRO] Classe principal nao definida. Configure MAIN_CLASS no script.
    goto :end
)
echo [INFO] Executando classe: %MAIN_CLASS%
cd /d "%WORK_DIR%"
"%JAVA_HOME%\bin\java.exe" %JVM_ARGS% -cp "%FULL_CLASSPATH%" %MAIN_CLASS% %APP_ARGS%
goto :end

:run_jar_custom
echo [INFO] Executar JAR com argumentos personalizados
set /p custom_args="Digite os argumentos: "
cd /d "%WORK_DIR%"
"%JAVA_HOME%\bin\java.exe" %JVM_ARGS% -jar "%JAR_FILE%" %custom_args%
goto :end

:run_class_custom
if "%MAIN_CLASS%"=="" (
    echo [ERRO] Classe principal nao definida. Configure MAIN_CLASS no script.
    goto :end
)
echo [INFO] Executar classe com argumentos personalizados
set /p custom_args="Digite os argumentos: "
cd /d "%WORK_DIR%"
"%JAVA_HOME%\bin\java.exe" %JVM_ARGS% -cp "%FULL_CLASSPATH%" %MAIN_CLASS% %custom_args%
goto :end

:jar_info
if not exist "%JAR_FILE%" (
    echo [ERRO] Arquivo JAR nao encontrado: %JAR_FILE%
    goto :end
)
echo [INFO] Informacoes do JAR: %JAR_FILE%
"%JAVA_HOME%\bin\jar.exe" -tf "%JAR_FILE%" | findstr "META-INF/MANIFEST.MF"
echo [INFO] Manifest:
"%JAVA_HOME%\bin\jar.exe" -xf "%JAR_FILE%" META-INF/MANIFEST.MF
type META-INF\MANIFEST.MF
del /q META-INF\MANIFEST.MF 2>nul
rmdir META-INF 2>nul
goto :end

:list_classes
if not exist "%JAR_FILE%" (
    echo [ERRO] Arquivo JAR nao encontrado: %JAR_FILE%
    goto :end
)
echo [INFO] Classes no JAR: %JAR_FILE%
"%JAVA_HOME%\bin\jar.exe" -tf "%JAR_FILE%" | findstr "\.class$"
goto :end

:check_dependencies
echo [INFO] Verificando dependencias...
echo [INFO] Java Version:
"%JAVA_HOME%\bin\java.exe" -version
echo [INFO] Classpath: %FULL_CLASSPATH%
if exist "%JAR_FILE%" (
    echo [INFO] JAR encontrado: %JAR_FILE%
) else (
    echo [ERRO] JAR nao encontrado: %JAR_FILE%
)
goto :end

:interactive_mode
echo [INFO] Modo interativo - Digite comandos Java diretamente
echo [INFO] Digite 'exit' para sair
cd /d "%WORK_DIR%"
:interactive_loop
set /p java_cmd="java> "
if "%java_cmd%"=="exit" goto :end
"%JAVA_HOME%\bin\java.exe" %java_cmd%
goto :interactive_loop

:configure_app
echo [INFO] Configuracao da aplicacao
echo [INFO] Configuracao atual:
echo [INFO] JAR_FILE=%JAR_FILE%
echo [INFO] MAIN_CLASS=%MAIN_CLASS%
echo [INFO] WORK_DIR=%WORK_DIR%
echo [INFO] JVM_ARGS=%JVM_ARGS%
echo [INFO] APP_ARGS=%APP_ARGS%
echo.
echo [INFO] Para alterar, edite as variaveis no inicio do script
goto :end

:invalid_option
echo [ERRO] Opcao invalida!
goto :end

:end
echo.
echo [INFO] Operacao concluida.
echo [INFO] Pressione qualquer tecla para sair...
pause >nul

REM Restaura as variaveis originais (opcional, pois o script termina)
REM set JAVA_HOME=%ORIGINAL_JAVA_HOME%
REM set PATH=%ORIGINAL_PATH%

endlocal
