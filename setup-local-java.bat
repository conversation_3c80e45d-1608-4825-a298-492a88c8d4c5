@echo off
REM Script para configurar Java local para SatMobWeb
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

setlocal

echo ========================================
echo CONFIGURACAO DE JAVA LOCAL - SatMobWeb
echo ========================================
echo.

REM =============================================================================
REM CONFIGURACOES
REM =============================================================================

set JAVA_BASE_DIR=C:\java
set DOWNLOAD_DIR=%TEMP%\java-setup

echo [INFO] Este script ajudara voce a configurar uma instalacao local do Java
echo [INFO] para usar com o SatMobWeb sem conflitar com o Java do sistema.
echo.

REM =============================================================================
REM VERIFICACAO DE JAVA EXISTENTE
REM =============================================================================

echo [INFO] Verificando Java atual do sistema...
java -version 2>nul
if %errorlevel% equ 0 (
    echo [INFO] Java encontrado no sistema. Versao:
    java -version
) else (
    echo [AVISO] Java nao encontrado no PATH do sistema
)
echo.

REM =============================================================================
REM CRIACAO DE DIRETORIO
REM =============================================================================

echo [INFO] Criando diretorio para Java local: %JAVA_BASE_DIR%
if not exist "%JAVA_BASE_DIR%" (
    mkdir "%JAVA_BASE_DIR%"
    echo [INFO] Diretorio criado: %JAVA_BASE_DIR%
) else (
    echo [INFO] Diretorio ja existe: %JAVA_BASE_DIR%
)
echo.

REM =============================================================================
REM OPCOES DE INSTALACAO
REM =============================================================================

echo [INFO] Opcoes de instalacao:
echo [1] Baixar OpenJDK 11 (Recomendado para aplicacoes web)
echo [2] Baixar OpenJDK 8 (Compatibilidade legacy)
echo [3] Baixar OpenJDK 17 (Versao LTS mais recente)
echo [4] Usar Java ja instalado em outro local
echo [5] Apenas mostrar instrucoes manuais
echo [0] Sair
echo.

set /p opcao="Digite sua opcao (0-5): "

if "%opcao%"=="1" goto :download_jdk11
if "%opcao%"=="2" goto :download_jdk8
if "%opcao%"=="3" goto :download_jdk17
if "%opcao%"=="4" goto :use_existing
if "%opcao%"=="5" goto :manual_instructions
if "%opcao%"=="0" goto :end
goto :invalid_option

REM =============================================================================
REM DOWNLOAD JDK 11
REM =============================================================================

:download_jdk11
echo [INFO] Preparando download do OpenJDK 11...
echo [INFO] Sera aberto o navegador para download manual.
echo [INFO] Baixe o arquivo ZIP do OpenJDK 11 para Windows x64
echo [INFO] URL: https://adoptium.net/temurin/releases/?version=11
echo.
pause
start https://adoptium.net/temurin/releases/?version=11
goto :extract_instructions

REM =============================================================================
REM DOWNLOAD JDK 8
REM =============================================================================

:download_jdk8
echo [INFO] Preparando download do OpenJDK 8...
echo [INFO] Sera aberto o navegador para download manual.
echo [INFO] Baixe o arquivo ZIP do OpenJDK 8 para Windows x64
echo [INFO] URL: https://adoptium.net/temurin/releases/?version=8
echo.
pause
start https://adoptium.net/temurin/releases/?version=8
goto :extract_instructions

REM =============================================================================
REM DOWNLOAD JDK 17
REM =============================================================================

:download_jdk17
echo [INFO] Preparando download do OpenJDK 17...
echo [INFO] Sera aberto o navegador para download manual.
echo [INFO] Baixe o arquivo ZIP do OpenJDK 17 para Windows x64
echo [INFO] URL: https://adoptium.net/temurin/releases/?version=17
echo.
pause
start https://adoptium.net/temurin/releases/?version=17
goto :extract_instructions

REM =============================================================================
REM INSTRUCOES DE EXTRACAO
REM =============================================================================

:extract_instructions
echo.
echo [INFO] ========================================
echo [INFO] INSTRUCOES DE INSTALACAO
echo [INFO] ========================================
echo [INFO] 1. Baixe o arquivo ZIP do OpenJDK
echo [INFO] 2. Extraia o conteudo para: %JAVA_BASE_DIR%
echo [INFO] 3. Renomeie a pasta extraida para um nome simples
echo [INFO]    Exemplo: jdk-11.0.20 ou jdk-8u391
echo [INFO] 4. Estrutura final deve ser:
echo [INFO]    %JAVA_BASE_DIR%\jdk-11.0.20\bin\java.exe
echo [INFO] 5. Execute o script run-with-local-java.bat
echo [INFO] 6. Edite a variavel LOCAL_JAVA_HOME no script
echo.
goto :end

REM =============================================================================
REM USAR JAVA EXISTENTE
REM =============================================================================

:use_existing
echo.
echo [INFO] Para usar uma instalacao existente do Java:
echo [INFO] 1. Localize o diretorio de instalacao do Java
echo [INFO] 2. Edite o arquivo run-with-local-java.bat
echo [INFO] 3. Altere a variavel LOCAL_JAVA_HOME para o caminho correto
echo [INFO] 
echo [INFO] Exemplos de caminhos comuns:
echo [INFO] - C:\Program Files\Java\jdk-11.0.20
echo [INFO] - C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
echo [INFO] - C:\java\jdk-11.0.20
echo.
goto :end

REM =============================================================================
REM INSTRUCOES MANUAIS
REM =============================================================================

:manual_instructions
echo.
echo [INFO] ========================================
echo [INFO] INSTRUCOES MANUAIS COMPLETAS
echo [INFO] ========================================
echo.
echo [INFO] PASSO 1: Baixar Java
echo [INFO] - Acesse: https://adoptium.net/temurin/releases/
echo [INFO] - Escolha a versao (recomendado: Java 11)
echo [INFO] - Baixe o arquivo ZIP para Windows x64
echo.
echo [INFO] PASSO 2: Instalar localmente
echo [INFO] - Crie o diretorio: %JAVA_BASE_DIR%
echo [INFO] - Extraia o ZIP baixado para este diretorio
echo [INFO] - Renomeie a pasta para algo simples (ex: jdk-11.0.20)
echo.
echo [INFO] PASSO 3: Configurar script
echo [INFO] - Edite o arquivo: run-with-local-java.bat
echo [INFO] - Altere a linha: set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20
echo [INFO] - Use o caminho correto da sua instalacao
echo.
echo [INFO] PASSO 4: Executar
echo [INFO] - Execute: run-with-local-java.bat
echo [INFO] - Escolha a opcao desejada no menu
echo.
echo [INFO] PASSO 5: GlassFish (opcional)
echo [INFO] - Se usar GlassFish separado, configure GLASSFISH_HOME
echo [INFO] - Baixe em: https://javaee.github.io/glassfish/download
echo.
goto :end

:invalid_option
echo [ERRO] Opcao invalida!
goto :end

:end
echo.
echo [INFO] ========================================
echo [INFO] CONFIGURACAO CONCLUIDA
echo [INFO] ========================================
echo [INFO] Proximos passos:
echo [INFO] 1. Siga as instrucoes mostradas acima
echo [INFO] 2. Execute run-with-local-java.bat para testar
echo [INFO] 3. Configure as variaveis no script conforme necessario
echo.
echo [INFO] Arquivos criados:
echo [INFO] - run-with-local-java.bat (Windows)
echo [INFO] - run-with-local-java.sh (Linux/Mac)
echo [INFO] - setup-local-java.bat (este arquivo)
echo.
pause

endlocal
