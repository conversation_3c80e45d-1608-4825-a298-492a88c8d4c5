# Executar Aplicações Java com Instalação Local

Este conjunto de scripts permite executar qualquer aplicação Java usando uma instalação local do Java, sem conflitar com a instalação do Java do sistema.

## 📁 Arquivos Criados

- `run-java-app.bat` - Script simples (arrastar e soltar)
- `java-runner.bat` - Script de linha de comando
- `run-with-local-java.bat` - Script avançado com menu
- `run-with-local-java.sh` - Script para Linux/Mac
- `setup-local-java.bat` - Script de configuração
- `README-Java-Local.md` - Este arquivo de documentação

## 🚀 Início Rápido

### Método 1: Arrastar e Soltar (Mais Fácil)

1. **Configure o Java local:**
   ```cmd
   setup-local-java.bat
   ```

2. **Execute qualquer aplicação:**
   - Arraste um arquivo `.jar` sobre `run-java-app.bat`
   - Ou clique duplo no script e digite o caminho

### Método 2: Linha de Comando

1. **Execute diretamente:**
   ```cmd
   java-runner.bat minha-app.jar
   java-runner.bat app.jar --port 8080
   ```

### Método 3: Menu Avançado

1. **Para múltiplas opções:**
   ```cmd
   run-with-local-java.bat
   ```

### Linux/Mac

1. **Torne o script executável:**
   ```bash
   chmod +x run-with-local-java.sh
   ```

2. **Configure o Java local:**
   - Baixe e extraia o OpenJDK para `/opt/java/jdk-11.0.20`
   - Edite as variáveis no script se necessário

3. **Execute a aplicação:**
   ```bash
   ./run-with-local-java.sh
   ```

## ⚙️ Configuração Detalhada

### 1. Download do Java

**Opções recomendadas:**
- **Java 11** (LTS) - Recomendado para aplicações web modernas
- **Java 8** - Para compatibilidade com sistemas legados
- **Java 17** (LTS) - Versão mais recente para novos projetos

**Fontes de download:**
- [Eclipse Adoptium](https://adoptium.net/temurin/releases/) (Recomendado)
- [Oracle OpenJDK](https://jdk.java.net/)
- [Amazon Corretto](https://aws.amazon.com/corretto/)

### 2. Estrutura de Diretórios

```
C:\java\                    (Windows)
├── jdk-11.0.20\
│   ├── bin\
│   │   ├── java.exe
│   │   ├── javac.exe
│   │   └── ...
│   ├── lib\
│   └── ...

/opt/java/                  (Linux/Mac)
├── jdk-11.0.20/
│   ├── bin/
│   │   ├── java
│   │   ├── javac
│   │   └── ...
│   ├── lib/
│   └── ...
```

### 3. Configuração das Variáveis

**Windows (`run-with-local-java.bat`):**
```batch
set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20
set GLASSFISH_HOME=C:\glassfish\glassfish5
set SERVER_PORT=8080
```

**Linux/Mac (`run-with-local-java.sh`):**
```bash
LOCAL_JAVA_HOME="/opt/java/jdk-11.0.20"
GLASSFISH_HOME="/opt/glassfish/glassfish5"
SERVER_PORT=8080
```

## 🎯 Funcionalidades dos Scripts

### Menu de Opções

1. **Compilar e executar** - `ant run`
2. **Apenas compilar** - `ant compile`
3. **Limpar e compilar** - `ant clean compile`
4. **Gerar WAR** - `ant dist`
5. **Executar testes** - `ant test`
6. **Iniciar GlassFish** - Inicia o servidor manualmente
7. **Parar GlassFish** - Para o servidor
8. **Status GlassFish** - Verifica status do servidor

### Validações Automáticas

- ✅ Verifica se o Java local existe
- ✅ Valida executáveis necessários
- ✅ Configura variáveis de ambiente temporariamente
- ✅ Mostra informações da versão do Java
- ✅ Restaura ambiente original ao finalizar

## 🔧 Resolução de Problemas

### Erro: "Java não encontrado"

**Causa:** Caminho incorreto na variável `LOCAL_JAVA_HOME`

**Solução:**
1. Verifique se o diretório existe
2. Confirme que existe `bin/java.exe` (Windows) ou `bin/java` (Linux/Mac)
3. Edite a variável no script com o caminho correto

### Erro: "GlassFish não encontrado"

**Causa:** GlassFish não instalado ou caminho incorreto

**Solução:**
1. Baixe o GlassFish: https://javaee.github.io/glassfish/download
2. Extraia para o diretório configurado
3. Ou comente/remova as linhas do GlassFish se usar outro servidor

### Erro de Compilação

**Causa:** Dependências ou configuração do projeto

**Solução:**
1. Execute `ant clean` primeiro
2. Verifique se todas as bibliotecas estão no lugar
3. Confirme que o `build.xml` está correto

## 📋 Requisitos

### Sistema
- Windows 10+ ou Linux/Mac
- Ant instalado e no PATH
- Acesso à internet para download do Java

### Java
- OpenJDK 8, 11, ou 17
- Mínimo 512MB de RAM para a JVM
- Espaço em disco: ~200MB para o JDK

### Aplicação
- Projeto SatMobWeb configurado
- Dependências no diretório `lib/`
- Arquivo `build.xml` válido

## 🎨 Personalização

### Adicionar Novas Opções

Edite o script e adicione novas funções:

```batch
REM Windows
:nova_funcao
echo [INFO] Executando nova funcao...
ant nova-tarefa
goto :end
```

```bash
# Linux/Mac
nova_funcao() {
    log_info "Executando nova funcao..."
    ant nova-tarefa
}
```

### Configurar Múltiplas Versões

Crie scripts separados para cada versão:
- `run-with-java8.bat`
- `run-with-java11.bat`
- `run-with-java17.bat`

## 📞 Suporte

### Logs e Debug

Os scripts mostram informações detalhadas durante a execução. Para debug adicional:

**Windows:**
```cmd
set ANT_OPTS=-verbose
run-with-local-java.bat
```

**Linux/Mac:**
```bash
export ANT_OPTS="-verbose"
./run-with-local-java.sh
```

### Verificação Manual

Para testar o Java local manualmente:

```bash
# Definir variáveis
export JAVA_HOME=/caminho/para/java
export PATH=$JAVA_HOME/bin:$PATH

# Testar
java -version
javac -version
```

## 📝 Notas Importantes

- ⚠️ Os scripts não modificam as configurações globais do sistema
- ⚠️ As variáveis de ambiente são temporárias (apenas durante a execução)
- ⚠️ Sempre teste em ambiente de desenvolvimento primeiro
- ⚠️ Mantenha backups dos arquivos de configuração originais

## 🔄 Atualizações

Para atualizar o Java local:
1. Baixe a nova versão
2. Extraia para um novo diretório
3. Atualize a variável `LOCAL_JAVA_HOME` no script
4. Teste a aplicação

---

**Criado em:** 2025-06-16  
**Versão:** 1.0  
**Compatibilidade:** SatMobWeb com Ant + GlassFish
