@echo off
REM Script simples para executar aplicacoes Java com instalacao local
REM Uso: Arraste e solte um arquivo JAR sobre este script
REM      ou execute: run-java-app.bat arquivo.jar [argumentos]
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

setlocal

REM =============================================================================
REM CONFIGURACAO RAPIDA - EDITE APENAS ESTA LINHA
REM =============================================================================

REM Caminho para sua instalacao local do Java
set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20

REM =============================================================================
REM VALIDACAO E CONFIGURACAO
REM =============================================================================

if not exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
    echo.
    echo [ERRO] Java nao encontrado em: %LOCAL_JAVA_HOME%
    echo.
    echo [INFO] COMO CORRIGIR:
    echo [INFO] 1. Baixe o Java de: https://adoptium.net/temurin/releases/
    echo [INFO] 2. Extraia para C:\java\jdk-11.0.20 (ou outro local)
    echo [INFO] 3. Edite a linha LOCAL_JAVA_HOME neste script
    echo.
    pause
    exit /b 1
)

REM Configura Java local temporariamente
set JAVA_HOME=%LOCAL_JAVA_HOME%
set PATH=%LOCAL_JAVA_HOME%\bin;%PATH%

echo.
echo ========================================
echo EXECUTANDO COM JAVA LOCAL
echo ========================================
echo Java: %JAVA_HOME%
java -version
echo ========================================
echo.

REM =============================================================================
REM PROCESSAMENTO DE ARGUMENTOS
REM =============================================================================

if "%~1"=="" goto :no_file

set JAR_FILE=%~1
set ARGS=%~2 %~3 %~4 %~5 %~6 %~7 %~8 %~9

REM Remove espacos extras
for /f "tokens=* delims= " %%a in ("%ARGS%") do set ARGS=%%a

REM =============================================================================
REM VALIDACAO DO ARQUIVO
REM =============================================================================

if not exist "%JAR_FILE%" (
    echo [ERRO] Arquivo nao encontrado: %JAR_FILE%
    goto :end
)

echo [INFO] Executando: %JAR_FILE%
if not "%ARGS%"=="" echo [INFO] Argumentos: %ARGS%
echo.

REM =============================================================================
REM EXECUCAO
REM =============================================================================

REM Muda para o diretorio do JAR para execucao
pushd "%~dp1"

REM Executa a aplicacao
if "%ARGS%"=="" (
    java -jar "%JAR_FILE%"
) else (
    java -jar "%JAR_FILE%" %ARGS%
)

set EXIT_CODE=%errorlevel%
popd

echo.
if %EXIT_CODE% equ 0 (
    echo [INFO] Aplicacao finalizada com sucesso
) else (
    echo [AVISO] Aplicacao finalizada com codigo: %EXIT_CODE%
)

goto :end

REM =============================================================================
REM TRATAMENTO DE ERRO
REM =============================================================================

:no_file
echo.
echo [INFO] ========================================
echo [INFO] COMO USAR ESTE SCRIPT
echo [INFO] ========================================
echo.
echo [INFO] OPCAO 1 - Arrastar e Soltar:
echo [INFO]   Arraste um arquivo .jar sobre este script
echo.
echo [INFO] OPCAO 2 - Linha de Comando:
echo [INFO]   %~nx0 arquivo.jar
echo [INFO]   %~nx0 arquivo.jar argumentos aqui
echo.
echo [INFO] EXEMPLOS:
echo [INFO]   %~nx0 minha-app.jar
echo [INFO]   %~nx0 app.jar --port 8080
echo [INFO]   %~nx0 "C:\Apps\programa.jar" -config cfg.xml
echo.
echo [INFO] CONFIGURACAO ATUAL:
echo [INFO]   Java Local: %LOCAL_JAVA_HOME%
echo.
echo [INFO] Se o Java nao estiver instalado, execute:
echo [INFO]   setup-local-java.bat
echo.

:end
echo.
echo [INFO] Pressione qualquer tecla para fechar...
pause >nul
endlocal
