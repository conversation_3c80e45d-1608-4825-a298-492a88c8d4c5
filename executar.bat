@echo off
REM Script para executar aplicacao Java com Java local
REM Edite as variaveis e execute

REM =============================================================================
REM CONFIGURACAO
REM =============================================================================

set JAVA_LOCAL=C:\java\jdk-11.0.20\bin\java.exe
set APLICACAO=C:\caminho\para\aplicacao.jar

REM =============================================================================
REM EXECUCAO
REM =============================================================================

"%JAVA_LOCAL%" -jar "%APLICACAO%"
pause
