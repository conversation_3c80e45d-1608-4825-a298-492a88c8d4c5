@echo off
REM Script generico para executar aplicacoes Java com instalacao local
REM Uso: java-runner.bat [arquivo.jar] [argumentos...]
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

setlocal enabledelayedexpansion

REM =============================================================================
REM CONFIGURACOES - EDITE CONFORME NECESSARIO
REM =============================================================================

REM Caminho para a instalacao local do Java
set LOCAL_JAVA_HOME=C:\java\jdk-11.0.20

REM Argumentos padrao da JVM
set DEFAULT_JVM_ARGS=-Xmx1g -Xms256m

REM =============================================================================
REM VALIDACOES
REM =============================================================================

if not exist "%LOCAL_JAVA_HOME%" (
    echo [ERRO] Java local nao encontrado: %LOCAL_JAVA_HOME%
    echo [INFO] Edite a variavel LOCAL_JAVA_HOME no script
    pause
    exit /b 1
)

if not exist "%LOCAL_JAVA_HOME%\bin\java.exe" (
    echo [ERRO] java.exe nao encontrado em: %LOCAL_JAVA_HOME%\bin\
    pause
    exit /b 1
)

REM =============================================================================
REM CONFIGURACAO DO AMBIENTE
REM =============================================================================

REM Salva variaveis originais
set ORIGINAL_JAVA_HOME=%JAVA_HOME%
set ORIGINAL_PATH=%PATH%

REM Configura Java local
set JAVA_HOME=%LOCAL_JAVA_HOME%
set PATH=%LOCAL_JAVA_HOME%\bin;%PATH%

echo [INFO] Usando Java: %JAVA_HOME%
echo [INFO] Versao:
java -version
echo.

REM =============================================================================
REM PROCESSAMENTO DE ARGUMENTOS
REM =============================================================================

if "%~1"=="" goto :show_usage
if "%~1"=="--help" goto :show_help
if "%~1"=="-h" goto :show_help

set JAR_FILE=%~1
set APP_ARGS=%~2 %~3 %~4 %~5 %~6 %~7 %~8 %~9

REM Remove espacos extras
set APP_ARGS=%APP_ARGS:  = %

REM =============================================================================
REM VALIDACAO DO ARQUIVO
REM =============================================================================

if not exist "%JAR_FILE%" (
    echo [ERRO] Arquivo nao encontrado: %JAR_FILE%
    echo [INFO] Verifique se o caminho esta correto
    goto :end
)

REM Verifica se e um arquivo JAR
echo %JAR_FILE% | findstr /i "\.jar$" >nul
if %errorlevel% neq 0 (
    echo [AVISO] Arquivo nao parece ser um JAR: %JAR_FILE%
    echo [INFO] Tentando executar mesmo assim...
)

REM =============================================================================
REM EXECUCAO
REM =============================================================================

echo [INFO] ========================================
echo [INFO] EXECUTANDO APLICACAO JAVA
echo [INFO] ========================================
echo [INFO] Arquivo: %JAR_FILE%
echo [INFO] Argumentos: %APP_ARGS%
echo [INFO] JVM Args: %DEFAULT_JVM_ARGS%
echo [INFO] Diretorio: %CD%
echo [INFO] ========================================
echo.

REM Executa a aplicacao
java %DEFAULT_JVM_ARGS% -jar "%JAR_FILE%" %APP_ARGS%

set EXIT_CODE=%errorlevel%
echo.
echo [INFO] Aplicacao finalizada com codigo: %EXIT_CODE%
goto :end

REM =============================================================================
REM FUNCOES DE AJUDA
REM =============================================================================

:show_usage
echo.
echo [INFO] USO: %~nx0 [arquivo.jar] [argumentos...]
echo.
echo [INFO] Exemplos:
echo [INFO]   %~nx0 minha-app.jar
echo [INFO]   %~nx0 app.jar --config config.xml
echo [INFO]   %~nx0 C:\apps\programa.jar -port 8080
echo.
echo [INFO] Para mais opcoes: %~nx0 --help
goto :end

:show_help
echo.
echo [INFO] ========================================
echo [INFO] JAVA RUNNER - AJUDA
echo [INFO] ========================================
echo.
echo [INFO] DESCRICAO:
echo [INFO]   Executa aplicacoes Java usando instalacao local
echo [INFO]   sem conflitar com Java do sistema
echo.
echo [INFO] USO:
echo [INFO]   %~nx0 [arquivo.jar] [argumentos...]
echo.
echo [INFO] OPCOES:
echo [INFO]   --help, -h    Mostra esta ajuda
echo.
echo [INFO] EXEMPLOS:
echo [INFO]   %~nx0 app.jar
echo [INFO]   %~nx0 app.jar --port 8080 --debug
echo [INFO]   %~nx0 "C:\Program Files\App\app.jar" -config cfg.xml
echo.
echo [INFO] CONFIGURACAO:
echo [INFO]   Java Local: %LOCAL_JAVA_HOME%
echo [INFO]   JVM Args: %DEFAULT_JVM_ARGS%
echo.
echo [INFO] NOTAS:
echo [INFO]   - O script usa Java local para evitar conflitos
echo [INFO]   - Argumentos sao passados diretamente para a aplicacao
echo [INFO]   - Use aspas para caminhos com espacos
echo [INFO]   - O diretorio atual e usado como working directory
echo.
goto :end

:end
REM Restaura variaveis originais
set JAVA_HOME=%ORIGINAL_JAVA_HOME%
set PATH=%ORIGINAL_PATH%

if not "%EXIT_CODE%"=="" exit /b %EXIT_CODE%
endlocal
