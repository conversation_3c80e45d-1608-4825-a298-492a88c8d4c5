@echo off
REM Template para executar aplicacao Java especifica
REM Copie este arquivo e edite as variaveis conforme necessario

REM =============================================================================
REM CONFIGURACAO DA APLICACAO
REM =============================================================================

REM Java local (para nao conflitar com SatMobWeb)
set JAVA_HOME_LOCAL=C:\java\jdk-11.0.20
set JAVA_EXE=%JAVA_HOME_LOCAL%\bin\java.exe

REM Aplicacao Java
set APP_NAME=MinhaAplicacao
set APP_JAR=C:\Apps\minha-aplicacao.jar
set APP_ARGS=--config config.xml --port 8080

REM Configuracoes da JVM
set MEMORY_MIN=256m
set MEMORY_MAX=1g
set JVM_OPTS=-Xms%MEMORY_MIN% -Xmx%MEMORY_MAX%

REM Diretorio de trabalho (opcional)
set WORK_DIR=%~dp0

REM =============================================================================
REM VALIDACAO E EXECUCAO
REM =============================================================================

echo Iniciando %APP_NAME%...

REM Verifica Java
if not exist "%JAVA_EXE%" (
    echo ERRO: Java nao encontrado em %JAVA_EXE%
    pause
    exit /b 1
)

REM Verifica aplicacao
if not exist "%APP_JAR%" (
    echo ERRO: Aplicacao nao encontrada em %APP_JAR%
    pause
    exit /b 1
)

REM Muda para diretorio de trabalho se especificado
if not "%WORK_DIR%"=="" cd /d "%WORK_DIR%"

REM Executa aplicacao
echo Executando: %APP_JAR%
echo Argumentos: %APP_ARGS%
echo.

"%JAVA_EXE%" %JVM_OPTS% -jar "%APP_JAR%" %APP_ARGS%

echo.
echo %APP_NAME% finalizada.
pause
