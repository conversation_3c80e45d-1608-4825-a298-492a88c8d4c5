#!/bin/bash
# Script para rodar SatMobWeb com Java local sem conflitar com instalacao do sistema
# Autor: Script gerado automaticamente
# Data: 2025-06-16

# =============================================================================
# CONFIGURACOES - EDITE ESTAS VARIAVEIS CONFORME SUA INSTALACAO
# =============================================================================

# Caminho para a instalacao local do Java (JDK ou JRE)
# Exemplo: /opt/java/jdk-11.0.20 ou /usr/local/java/jdk-8u391
LOCAL_JAVA_HOME="/opt/java/jdk-11.0.20"

# Caminho para o GlassFish Server (se necessario)
# Exemplo: /opt/glassfish/glassfish5
GLASSFISH_HOME="/opt/glassfish/glassfish5"

# Porta do servidor (padrao GlassFish: 8080)
SERVER_PORT=8080

# =============================================================================
# CORES PARA OUTPUT
# =============================================================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# FUNCOES AUXILIARES
# =============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERRO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCESSO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[AVISO]${NC} $1"
}

# =============================================================================
# VALIDACOES
# =============================================================================

log_info "Verificando instalacao local do Java..."

if [ ! -d "$LOCAL_JAVA_HOME" ]; then
    log_error "Diretorio do Java nao encontrado: $LOCAL_JAVA_HOME"
    log_info "Por favor, edite a variavel LOCAL_JAVA_HOME no script"
    log_info "Exemplo: LOCAL_JAVA_HOME=\"/opt/java/jdk-11.0.20\""
    exit 1
fi

if [ ! -f "$LOCAL_JAVA_HOME/bin/java" ]; then
    log_error "Executavel java nao encontrado em: $LOCAL_JAVA_HOME/bin/"
    log_info "Verifique se o caminho esta correto"
    exit 1
fi

# =============================================================================
# CONFIGURACAO DO AMBIENTE
# =============================================================================

log_info "Configurando ambiente com Java local..."

# Salva as variaveis originais
ORIGINAL_JAVA_HOME=$JAVA_HOME
ORIGINAL_PATH=$PATH

# Define o Java local como prioritario
export JAVA_HOME=$LOCAL_JAVA_HOME
export PATH=$LOCAL_JAVA_HOME/bin:$PATH

# Configuracoes do GlassFish (se especificado)
if [ -d "$GLASSFISH_HOME" ]; then
    log_info "Configurando GlassFish: $GLASSFISH_HOME"
    export AS_JAVA=$LOCAL_JAVA_HOME
    export PATH=$GLASSFISH_HOME/bin:$PATH
fi

# =============================================================================
# INFORMACOES DO AMBIENTE
# =============================================================================

echo
log_info "========================================"
log_info "CONFIGURACAO DO AMBIENTE"
log_info "========================================"
log_info "Java Home: $JAVA_HOME"
log_info "Java Version:"
"$JAVA_HOME/bin/java" -version
log_info "GlassFish: $GLASSFISH_HOME"
log_info "Porta: $SERVER_PORT"
log_info "========================================"
echo

# =============================================================================
# FUNCOES DE EXECUCAO
# =============================================================================

run_app() {
    log_info "Compilando e executando aplicacao..."
    ant run
}

compile_only() {
    log_info "Compilando aplicacao..."
    ant compile
}

clean_compile() {
    log_info "Limpando e compilando aplicacao..."
    ant clean compile
}

build_war() {
    log_info "Gerando arquivo WAR..."
    ant dist
    log_success "WAR gerado em: dist/SatMobWeb.war"
}

run_tests() {
    log_info "Executando testes..."
    ant test
}

start_glassfish() {
    if [ ! -d "$GLASSFISH_HOME" ]; then
        log_error "GlassFish nao configurado ou nao encontrado"
        return 1
    fi
    log_info "Iniciando GlassFish Server..."
    "$GLASSFISH_HOME/bin/asadmin" start-domain
    log_success "GlassFish iniciado. Acesse: http://localhost:$SERVER_PORT"
}

stop_glassfish() {
    if [ ! -d "$GLASSFISH_HOME" ]; then
        log_error "GlassFish nao configurado ou nao encontrado"
        return 1
    fi
    log_info "Parando GlassFish Server..."
    "$GLASSFISH_HOME/bin/asadmin" stop-domain
}

status_glassfish() {
    if [ ! -d "$GLASSFISH_HOME" ]; then
        log_error "GlassFish nao configurado ou nao encontrado"
        return 1
    fi
    log_info "Status do GlassFish Server..."
    "$GLASSFISH_HOME/bin/asadmin" list-domains
}

# =============================================================================
# MENU PRINCIPAL
# =============================================================================

show_menu() {
    echo
    log_info "Escolha uma opcao:"
    echo "  [1] Compilar e executar (ant run)"
    echo "  [2] Apenas compilar (ant compile)"
    echo "  [3] Limpar e compilar (ant clean compile)"
    echo "  [4] Gerar WAR (ant dist)"
    echo "  [5] Executar testes (ant test)"
    echo "  [6] Iniciar GlassFish manualmente"
    echo "  [7] Parar GlassFish"
    echo "  [8] Status do GlassFish"
    echo "  [0] Sair"
    echo
}

# =============================================================================
# LOOP PRINCIPAL
# =============================================================================

while true; do
    show_menu
    read -p "Digite sua opcao (0-8): " opcao
    
    case $opcao in
        1)
            run_app
            ;;
        2)
            compile_only
            ;;
        3)
            clean_compile
            ;;
        4)
            build_war
            ;;
        5)
            run_tests
            ;;
        6)
            start_glassfish
            ;;
        7)
            stop_glassfish
            ;;
        8)
            status_glassfish
            ;;
        0)
            log_info "Saindo..."
            break
            ;;
        *)
            log_error "Opcao invalida!"
            ;;
    esac
    
    echo
    read -p "Pressione Enter para continuar..."
done

# Restaura as variaveis originais (opcional)
export JAVA_HOME=$ORIGINAL_JAVA_HOME
export PATH=$ORIGINAL_PATH

log_success "Script finalizado."
