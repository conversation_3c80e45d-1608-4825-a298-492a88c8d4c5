@echo off
REM Script simples para executar aplicacao Java com instalacao local
REM Edite as variaveis abaixo e execute o script
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

REM =============================================================================
REM CONFIGURACAO - EDITE APENAS ESTAS LINHAS
REM =============================================================================

REM Caminho para o Java local
set JAVA_LOCAL=C:\java\jdk-11.0.20\bin\java.exe

REM Caminho para a aplicacao Java
set APP_JAR=C:\caminho\para\sua\aplicacao.jar

REM Argumentos da aplicacao (opcional)
set APP_ARGS=

REM Argumentos da JVM (opcional)
set JVM_ARGS=-Xmx512m

REM =============================================================================
REM EXECUCAO - NAO EDITE ABAIXO DESTA LINHA
REM =============================================================================

REM Verifica se o Java existe
if not exist "%JAVA_LOCAL%" (
    echo ERRO: Java nao encontrado em: %JAVA_LOCAL%
    echo Edite a variavel JAVA_LOCAL no script
    pause
    exit /b 1
)

REM Verifica se a aplicacao existe
if not exist "%APP_JAR%" (
    echo ERRO: Aplicacao nao encontrada em: %APP_JAR%
    echo Edite a variavel APP_JAR no script
    pause
    exit /b 1
)

REM Executa a aplicacao
echo Executando: %APP_JAR%
"%JAVA_LOCAL%" %JVM_ARGS% -jar "%APP_JAR%" %APP_ARGS%

REM Mostra codigo de saida
echo.
echo Aplicacao finalizada com codigo: %errorlevel%
pause
