@echo off
REM Exemplo de uso dos scripts Java locais
REM Este arquivo demonstra como usar os diferentes scripts
REM Autor: Script gerado automaticamente
REM Data: 2025-06-16

echo ========================================
echo EXEMPLO DE USO - JAVA LOCAL
echo ========================================
echo.

echo [INFO] Este arquivo demonstra como usar os scripts para executar
echo [INFO] aplicacoes Java com instalacao local, sem conflitar com
echo [INFO] o Java do sistema (usado pelo SatMobWeb).
echo.

REM =============================================================================
REM VERIFICACAO DE ARQUIVOS
REM =============================================================================

echo [INFO] Verificando scripts disponiveis...
echo.

if exist "run-java-app.bat" (
    echo [OK] run-java-app.bat - Script simples ^(arrastar e soltar^)
) else (
    echo [X] run-java-app.bat - NAO ENCONTRADO
)

if exist "java-runner.bat" (
    echo [OK] java-runner.bat - Script de linha de comando
) else (
    echo [X] java-runner.bat - NAO ENCONTRADO
)

if exist "run-with-local-java.bat" (
    echo [OK] run-with-local-java.bat - Script avancado com menu
) else (
    echo [X] run-with-local-java.bat - NAO ENCONTRADO
)

if exist "setup-local-java.bat" (
    echo [OK] setup-local-java.bat - Script de configuracao
) else (
    echo [X] setup-local-java.bat - NAO ENCONTRADO
)

echo.

REM =============================================================================
REM EXEMPLOS DE USO
REM =============================================================================

echo [INFO] ========================================
echo [INFO] EXEMPLOS DE USO
echo [INFO] ========================================
echo.

echo [INFO] 1. CONFIGURACAO INICIAL:
echo [INFO]    setup-local-java.bat
echo [INFO]    ^(Execute uma vez para baixar e configurar o Java^)
echo.

echo [INFO] 2. EXECUTAR JAR SIMPLES:
echo [INFO]    run-java-app.bat meu-programa.jar
echo [INFO]    ^(Ou arraste o JAR sobre o script^)
echo.

echo [INFO] 3. EXECUTAR COM ARGUMENTOS:
echo [INFO]    java-runner.bat app.jar --port 8080 --debug
echo [INFO]    java-runner.bat servidor.jar -config config.xml
echo.

echo [INFO] 4. MENU AVANCADO:
echo [INFO]    run-with-local-java.bat
echo [INFO]    ^(Abre menu com varias opcoes^)
echo.

echo [INFO] 5. EXEMPLOS REAIS:
echo.
echo [INFO]    REM Executar servidor web
echo [INFO]    java-runner.bat spring-boot-app.jar --server.port=9090
echo.
echo [INFO]    REM Executar ferramenta de linha de comando
echo [INFO]    java-runner.bat tool.jar --input data.csv --output result.xlsx
echo.
echo [INFO]    REM Executar aplicacao com interface grafica
echo [INFO]    run-java-app.bat desktop-app.jar
echo.

REM =============================================================================
REM CENARIOS COMUNS
REM =============================================================================

echo [INFO] ========================================
echo [INFO] CENARIOS COMUNS
echo [INFO] ========================================
echo.

echo [INFO] PROBLEMA: "Java nao encontrado"
echo [INFO] SOLUCAO: Execute setup-local-java.bat primeiro
echo.

echo [INFO] PROBLEMA: "Conflito com Java do SatMobWeb"
echo [INFO] SOLUCAO: Os scripts usam Java local isolado
echo.

echo [INFO] PROBLEMA: "Aplicacao nao inicia"
echo [INFO] SOLUCAO: Verifique se o JAR e executavel:
echo [INFO]          java-runner.bat --help
echo.

echo [INFO] PROBLEMA: "Erro de memoria"
echo [INFO] SOLUCAO: Edite JVM_ARGS nos scripts:
echo [INFO]          set JVM_ARGS=-Xmx2g -Xms512m
echo.

REM =============================================================================
REM TESTE RAPIDO
REM =============================================================================

echo [INFO] ========================================
echo [INFO] TESTE RAPIDO
echo [INFO] ========================================
echo.

set /p teste="Deseja testar a configuracao do Java local? (s/n): "

if /i "%teste%"=="s" (
    echo.
    echo [INFO] Testando configuracao...
    
    if exist "java-runner.bat" (
        echo [INFO] Executando: java-runner.bat --help
        call java-runner.bat --help
    ) else (
        echo [ERRO] Script java-runner.bat nao encontrado
    )
) else (
    echo [INFO] Teste cancelado pelo usuario
)

echo.

REM =============================================================================
REM PROXIMOS PASSOS
REM =============================================================================

echo [INFO] ========================================
echo [INFO] PROXIMOS PASSOS
echo [INFO] ========================================
echo.

echo [INFO] 1. Se ainda nao fez, execute: setup-local-java.bat
echo [INFO] 2. Teste com um JAR simples: run-java-app.bat
echo [INFO] 3. Para uso avancado: run-with-local-java.bat
echo [INFO] 4. Leia o README-Java-Local.md para detalhes
echo.

echo [INFO] DICAS:
echo [INFO] - Use run-java-app.bat para uso casual
echo [INFO] - Use java-runner.bat para automacao
echo [INFO] - Use run-with-local-java.bat para desenvolvimento
echo.

echo [INFO] SUPORTE:
echo [INFO] - Todos os scripts tem opcoes --help
echo [INFO] - Verifique o README-Java-Local.md
echo [INFO] - Os scripts sao auto-documentados
echo.

echo ========================================
echo EXEMPLO CONCLUIDO
echo ========================================
echo.
pause
